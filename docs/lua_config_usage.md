# Lua配置功能使用说明

## 概述

本项目实现了基于Lua脚本的动态配置功能，支持本地配置文件和远程服务器配置的无缝切换。主要用于引流名片页面的注意事项、UI配置、功能开关等动态配置管理。

## 功能特性

- ✅ **本地Lua文件解析**：支持从assets加载本地Lua配置文件
- ✅ **远程配置下载**：支持从服务器下载最新的Lua配置文件
- ✅ **缓存机制**：本地缓存远程配置，减少网络请求
- ✅ **热更新**：支持强制刷新配置，实现热更新
- ✅ **容错处理**：配置加载失败时自动回退到默认配置
- ✅ **多层级配置**：支持复杂的嵌套配置结构

## 文件结构

```
assets/configs/
└── traffic_diversion_config.lua    # 本地Lua配置文件

lib/service/
└── lua_config_service.dart         # Lua配置服务

lib/test/
└── lua_config_test_page.dart       # 配置测试页面
```

## Lua配置文件格式

### 基本结构

```lua
-- 引流名片配置文件
config = {
    version = "1.0.0",
    last_updated = "2024-01-15",
    author = "系统管理员"
}

-- 注意事项配置
notice_config = {
    title = "注意事项",
    show_refresh_button = true,
    items = {
        "1.注意事项内容1",
        "2.注意事项内容2",
        "3.注意事项内容3"
    }
}

-- 二维码相关配置
qr_config = {
    grid_size = 100,
    detail_size = 220,
    show_wechat_icon = true,
    max_count = 9
}

-- 功能开关
feature_flags = {
    enable_qr_recognition = true,
    enable_hero_animation = true,
    enable_swipe_navigation = true
}
```

### 支持的数据类型

- **字符串**：`key = "value"`
- **数字**：`key = 123` 或 `key = 12.34`
- **布尔值**：`key = true` 或 `key = false`
- **数组**：`key = {"item1", "item2", "item3"}`

## 使用方法

### 1. 基本使用

```dart
import 'package:npemployee/service/lua_config_service.dart';

// 创建服务实例
final configService = LuaConfigService();

// 获取注意事项配置
List<String> noticeItems = await configService.downloadNoticeConfig();

// 获取完整配置
Map<String, dynamic> fullConfig = await configService.getFullConfig();
```

### 2. 强制刷新配置

```dart
// 强制从服务器下载最新配置
List<String> noticeItems = await configService.downloadNoticeConfig(
  forceUpdate: true
);

Map<String, dynamic> fullConfig = await configService.getFullConfig(
  forceUpdate: true
);
```

### 3. 在Widget中使用

```dart
class MyWidget extends StatefulWidget {
  @override
  _MyWidgetState createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  List<String> _noticeItems = [];
  
  @override
  void initState() {
    super.initState();
    _loadConfig();
  }
  
  Future<void> _loadConfig() async {
    try {
      final configService = LuaConfigService();
      final config = await configService.getFullConfig();
      
      if (config.containsKey('notice_config')) {
        final noticeConfig = config['notice_config'] as Map<String, dynamic>;
        if (noticeConfig.containsKey('items')) {
          setState(() {
            _noticeItems = List<String>.from(noticeConfig['items']);
          });
        }
      }
    } catch (e) {
      print('配置加载失败: $e');
    }
  }
}
```

## 配置部署

### 本地开发

1. 修改 `assets/configs/traffic_diversion_config.lua` 文件
2. 重新运行应用，配置会自动加载

### 生产环境

1. 将Lua配置文件上传到服务器
2. 修改 `LuaConfigService` 中的URL指向您的服务器
3. 应用会自动下载并缓存最新配置

```dart
// 在 LuaConfigService 中修改URL
const url = 'https://your-domain.com/configs/traffic_diversion_config.lua';
```

## 测试功能

项目提供了测试页面来验证Lua配置功能：

```dart
import 'package:npemployee/test/lua_config_test_page.dart';

// 在路由中添加测试页面
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => LuaConfigTestPage()),
);
```

测试页面功能：
- 显示完整的配置信息
- 测试配置刷新功能
- 验证各种数据类型的解析
- 查看功能开关状态

## 错误处理

配置服务具有完善的错误处理机制：

1. **网络错误**：自动回退到本地缓存
2. **解析错误**：使用默认配置
3. **文件不存在**：使用内置默认值
4. **格式错误**：记录错误日志并使用默认配置

## 性能优化

- **缓存机制**：远程配置本地缓存24小时
- **懒加载**：只在需要时加载配置
- **异步处理**：所有配置操作都是异步的
- **内存优化**：配置数据结构简洁高效

## 注意事项

1. **Lua语法**：确保Lua文件语法正确
2. **编码格式**：使用UTF-8编码
3. **文件大小**：建议配置文件不超过1MB
4. **网络超时**：默认30秒超时，可根据需要调整
5. **权限要求**：需要网络访问权限

## 扩展功能

可以根据需要扩展更多配置类型：

```lua
-- UI主题配置
theme_config = {
    primary_color = "#007AFF",
    background_color = "#FFFFFF",
    text_color = "#000000"
}

-- 业务配置
business_config = {
    max_upload_size = 10485760,  -- 10MB
    allowed_file_types = {"jpg", "png", "gif"},
    api_timeout = 30
}
```

## 技术实现

- **解析器**：自定义正则表达式解析器
- **缓存**：基于文件系统的本地缓存
- **网络**：使用Dio进行HTTP请求
- **错误处理**：多层级错误处理和回退机制
