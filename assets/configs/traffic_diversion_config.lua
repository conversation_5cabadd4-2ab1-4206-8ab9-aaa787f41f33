-- 引流名片配置文件
-- 此文件包含注意事项、UI配置等信息

-- 配置版本信息
config = {
    version = "1.0.0",
    last_updated = "2024-01-15",
    author = "系统管理员"
}

-- 注意事项配置
notice_config = {
    title = "注意事项",
    show_refresh_button = true,
    items = {
        "1.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。",
        "2.请确保上传的微信二维码清晰可见，避免影响学员扫码体验。建议使用高清图片，避免模糊或变形。",
        "3.系统会自动为您的二维码添加微信图标，提升识别度和用户体验。",
        "4.建议定期更新您的联系方式，确保学员能够及时联系到您。过期的联系方式可能影响业务转化。",
        "5.如遇到技术问题或需要帮助，请及时联系客服获取专业支持。",
        "6.为保护您的隐私，请勿在公共场所展示包含个人信息的二维码。"
    }
}

-- 二维码相关配置
qr_config = {
    -- 九宫格中二维码大小
    grid_size = 100,
    -- 详情页二维码大小
    detail_size = 220,
    -- 是否显示微信图标
    show_wechat_icon = true,
    -- 微信图标大小
    icon_size = {
        grid = 16,
        detail = 28
    },
    -- 最大二维码数量
    max_count = 9
}

-- UI配置
ui_config = {
    -- 九宫格配置
    grid = {
        columns = 3,
        spacing = 10,
        border_radius = 8,
        border_color = "#E5E5E5",
        background_color = "#FFFFFF"
    },
    -- 添加按钮配置
    add_button = {
        background_color = "#F8F8F8",
        icon_color = "#999999",
        text = "选择图片",
        text_color = "#999999"
    },
    -- 详情页配置
    detail_page = {
        background_color = "#000000",
        container_background = "#FFFFFF",
        shadow_opacity = 0.3,
        border_radius = 16
    }
}

-- 功能开关
feature_flags = {
    -- 是否启用二维码识别功能
    enable_qr_recognition = true,
    -- 是否启用Hero动画
    enable_hero_animation = true,
    -- 是否启用左右滑动
    enable_swipe_navigation = true,
    -- 是否启用长按删除
    enable_long_press_delete = true,
    -- 是否启用点击空白区域返回
    enable_tap_to_dismiss = true,
    -- 是否启用配置热更新
    enable_hot_reload = true
}

-- 文本配置
text_config = {
    qr_code_title = "微信二维码",
    delete_dialog = {
        title = "删除二维码",
        content = "确定要删除这个二维码吗？",
        cancel = "取消",
        confirm = "确定"
    },
    messages = {
        qr_recognition_loading = "识别二维码中...",
        qr_recognition_success = "二维码识别成功",
        qr_recognition_failed = "未识别到二维码，请选择包含二维码的图片",
        file_read_failed = "图片文件读取失败",
        config_refresh_loading = "刷新配置中...",
        config_refresh_success = "配置刷新成功",
        config_refresh_failed = "配置刷新失败"
    },
    hints = {
        long_press_delete = "长按可删除",
        tap_to_dismiss = "点击空白区域返回",
        combined_hint = "长按可删除 · 点击空白区域返回"
    }
}

-- 返回配置对象
return {
    config = config,
    notice_config = notice_config,
    qr_config = qr_config,
    ui_config = ui_config,
    feature_flags = feature_flags,
    text_config = text_config
}
