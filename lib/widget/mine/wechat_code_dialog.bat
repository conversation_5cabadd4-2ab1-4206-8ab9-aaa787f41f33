import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:qr_code_dart_scan/qr_code_dart_scan.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../common/dialog/custom_dialog.dart';

Future<AssetEntity?> _pickFromCamera(BuildContext c) {
  return CameraPicker.pickFromCamera(
    c,
    pickerConfig: const CameraPickerConfig(enableRecording: true),
  );
}

class WechatCodeDialog extends StatefulWidget {
  final Function(String) onSuccess;
  final List? codes;
  const WechatCodeDialog({super.key, required this.onSuccess, this.codes});

  @override
  State<WechatCodeDialog> createState() => _WechatCodeDialogState();
}

class _WechatCodeDialogState extends State<WechatCodeDialog> {
  List<AssetEntity>? _assets;
  List? _codes;

  @override
  void initState() {
    super.initState();
    _codes = widget.codes ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.pop(context),
      child: Material(
        color: Colors.transparent,
        child: Center(
          child: Container(
            alignment: Alignment.center,
            color: Colors.transparent,
            width: 323.5.w,
            height: 424.h,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Image.asset('assets/png/mine/wechat_code_bac.png'),
                Positioned(
                    bottom: 19.5.h,
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 5.w, vertical: 10.h),
                      child: TextButton(
                          onPressed: () => _upload(),
                          child: Text(
                            _codes!.isEmpty ? '上传' : '重新上传',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 16.sp,
                                decoration: TextDecoration.underline,
                                decorationColor: Colors.white),
                          )),
                    )),
                if (_codes != null && _codes!.isNotEmpty)
                  Positioned(
                    bottom: 80.h,
                    child: Container(
                      padding: EdgeInsets.all(15.r),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16.r)),
                      child: QrImageView(
                        data: _codes!.first,
                        size: 194.r,
                      ),
                    ),
                  )
                else
                  Positioned(
                    width: 194.r + 15.r + 10,
                    height: 194.r + 15.r + 10,
                    bottom: 80.h,
                    child: Container(
                      padding: EdgeInsets.all(15.r),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16.r)),
                    ),
                  )
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _upload() async {
    final navigatorContext = Navigator.of(context);
    PermissionState state = await PhotoManager.getPermissionState(
        requestOption: const PermissionRequestOption());
    if (state.isAuth) {
      _checkPhotos();
    } else {
      if (Platform.isAndroid) {
        showDialog(
          context: context,
          builder: (_) => CustomDialog(
            title: "提示",
            content: "上传二维码需要相册权限",
            cancelButtonText: "取消",
            confirmButtonText: "确定",
            cancelButtonColor: AppTheme.colorButtonGrey,
            confirmButtonColor: AppTheme.colorBlue,
            onCancel: () {
              navigatorContext.pop();
            },
            onConfirm: () {
              navigatorContext.pop();
              PhotoManager.requestPermissionExtend(
                      requestOption: const PermissionRequestOption())
                  .then((value) {
                if (value.isAuth) {
                  _checkPhotos();
                }
              });
            },
          ),
        );
      } else {
        _checkPhotos();
      }
    }
  }

  void _checkPhotos() async {
    _assets = await AssetPicker.pickAssets(
      context,
      pickerConfig: AssetPickerConfig(
        maxAssets: 1,
        selectedAssets: _assets,
        specialItemPosition: SpecialItemPosition.prepend,
        pathNameBuilder: (AssetPathEntity path) => switch (path) {
          final p when p.isAll => '全部',
          _ => path.name,
        },
        specialItemBuilder: (
          BuildContext context,
          AssetPathEntity? path,
          int length,
        ) {
          if (path?.isAll != true) {
            return null;
          }
          return Semantics(
            // label: textDelegate.sActionUseCameraHint,
            button: true,
            // onTapHint: textDelegate.sActionUseCameraHint,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () async {
                Feedback.forTap(context);
                final AssetEntity? result = await _pickFromCamera(context);
                if (result != null) {
                  _assets = [result];
                  Navigator.maybeOf(context)?.pop();
                  _uploadCode(result);
                }
              },
              child: Container(
                padding: const EdgeInsets.all(28.0),
                color: Theme.of(context).dividerColor,
                child: const FittedBox(
                  fit: BoxFit.fill,
                  child: Icon(Icons.camera_enhance),
                ),
              ),
            ),
          );
        },
      ),
    );
    if (_assets != null && _assets!.isNotEmpty) {
      AssetEntity element = _assets!.first;
      _uploadCode(element);
    }
  }

  void _uploadCode(AssetEntity element) async {
    EasyLoading.show(status: '二维码识别中...');
    File? file = await element.file;
    if (file?.path != null) {
      final decoder = QRCodeDartScanDecoder(formats: [BarcodeFormat.qrCode]);
      Result? result = await decoder.decodeFile(XFile('${file?.path}'));
      if (result?.text != null) {
        EasyLoading.dismiss();
        EasyLoading.show(status: '正在上传...');
        UserServiceProvider().uploadMyCode([result!.text]).then((data) {
          EasyLoading.dismiss();
          if (data?.code == 0) {
            _codes?.clear();
            _codes?.add(result.text);
            EasyLoading.showSuccess('上传成功');
            widget.onSuccess(result.text);
            setState(() {});
          } else {
            EasyLoading.showError(data?.msg ?? '上传失败');
          }
        });
      } else {
        EasyLoading.dismiss();
        EasyLoading.showError('识别二维码失败，请重新操作');
      }
    } else {
      EasyLoading.dismiss();
      EasyLoading.showError('选择图片有误，请重新选择');
    }
  }
}
