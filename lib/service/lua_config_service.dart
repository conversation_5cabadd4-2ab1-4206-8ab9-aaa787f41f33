import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';

/// Lua配置服务
/// 负责从服务端下载Lua配置文件并解析
class LuaConfigService {
  static final LuaConfigService _instance = LuaConfigService._internal();
  factory LuaConfigService() => _instance;
  LuaConfigService._internal();

  final Dio _dio = Dio();

  /// 配置文件缓存路径
  Future<String> get _configCachePath async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/lua_configs';
  }

  /// 加载本地Lua配置文件
  Future<Map<String, dynamic>> loadLocalLuaConfig() async {
    try {
      // 从assets加载本地Lua文件
      final luaContent = await rootBundle
          .loadString('assets/configs/traffic_diversion_config.lua');
      return _parseLuaConfigAdvanced(luaContent);
    } catch (e) {
      debugPrint('加载本地Lua配置失败: $e');
      return _getDefaultConfig();
    }
  }

  /// 下载并解析注意事项配置
  Future<List<String>> downloadNoticeConfig({
    String? configUrl,
    bool forceUpdate = false,
  }) async {
    try {
      // 优先使用本地Lua配置
      if (!forceUpdate) {
        final localConfig = await loadLocalLuaConfig();
        if (localConfig.containsKey('notice_config')) {
          final noticeConfig =
              localConfig['notice_config'] as Map<String, dynamic>;
          if (noticeConfig.containsKey('items')) {
            return List<String>.from(noticeConfig['items']);
          }
        }
      }

      // 默认配置URL（实际项目中应该从配置中获取）
      final url = configUrl ??
          'https://your-server.com/configs/traffic_diversion_config.lua';

      // 获取缓存文件路径
      final cachePath = await _configCachePath;
      final cacheDir = Directory(cachePath);
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      final cacheFile = File('$cachePath/traffic_diversion_config.lua');

      // 检查是否需要下载
      if (!forceUpdate && await cacheFile.exists()) {
        // 检查缓存文件是否过期（例如：24小时）
        final lastModified = await cacheFile.lastModified();
        final now = DateTime.now();
        if (now.difference(lastModified).inHours < 24) {
          // 使用缓存文件
          final content = await cacheFile.readAsString();
          final config = _parseLuaConfigAdvanced(content);
          if (config.containsKey('notice_config')) {
            final noticeConfig =
                config['notice_config'] as Map<String, dynamic>;
            if (noticeConfig.containsKey('items')) {
              return List<String>.from(noticeConfig['items']);
            }
          }
        }
      }

      // 从服务端下载配置文件
      debugPrint('正在下载Lua配置文件: $url');
      final response = await _dio.get(url);

      if (response.statusCode == 200) {
        final content = response.data.toString();

        // 保存到缓存
        await cacheFile.writeAsString(content);

        // 解析并返回
        final config = _parseLuaConfigAdvanced(content);
        if (config.containsKey('notice_config')) {
          final noticeConfig = config['notice_config'] as Map<String, dynamic>;
          if (noticeConfig.containsKey('items')) {
            return List<String>.from(noticeConfig['items']);
          }
        }
      } else {
        throw Exception('下载配置文件失败: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('下载Lua配置失败: $e');

      // 尝试使用缓存文件
      final cachePath = await _configCachePath;
      final cacheFile = File('$cachePath/traffic_diversion_config.lua');
      if (await cacheFile.exists()) {
        final content = await cacheFile.readAsString();
        final config = _parseLuaConfigAdvanced(content);
        if (config.containsKey('notice_config')) {
          final noticeConfig = config['notice_config'] as Map<String, dynamic>;
          if (noticeConfig.containsKey('items')) {
            return List<String>.from(noticeConfig['items']);
          }
        }
      }

      // 返回默认配置
      return _getDefaultNoticeConfig();
    }

    // 确保总是有返回值
    return _getDefaultNoticeConfig();
  }

  /// 高级Lua配置解析器
  Map<String, dynamic> _parseLuaConfigAdvanced(String luaContent) {
    try {
      final Map<String, dynamic> config = {};

      // 解析配置块
      config['config'] = _parseConfigBlock(luaContent, 'config');
      config['notice_config'] = _parseConfigBlock(luaContent, 'notice_config');
      config['qr_config'] = _parseConfigBlock(luaContent, 'qr_config');
      config['ui_config'] = _parseConfigBlock(luaContent, 'ui_config');
      config['feature_flags'] = _parseConfigBlock(luaContent, 'feature_flags');
      config['text_config'] = _parseConfigBlock(luaContent, 'text_config');

      return config;
    } catch (e) {
      debugPrint('高级Lua解析失败: $e');
      return _getDefaultConfig();
    }
  }

  /// 解析单个配置块
  Map<String, dynamic> _parseConfigBlock(String luaContent, String blockName) {
    try {
      // 查找配置块
      final regex = RegExp(
        '$blockName\\s*=\\s*\\{([^}]+(?:\\{[^}]*\\}[^}]*)*)\\}',
        multiLine: true,
        dotAll: true,
      );
      final match = regex.firstMatch(luaContent);

      if (match != null) {
        final blockContent = match.group(1)!;
        return _parseBlockContent(blockContent);
      }

      return {};
    } catch (e) {
      debugPrint('解析配置块 $blockName 失败: $e');
      return {};
    }
  }

  /// 解析块内容
  Map<String, dynamic> _parseBlockContent(String content) {
    final Map<String, dynamic> result = {};

    // 解析字符串值
    final stringRegex = RegExp(r'(\w+)\s*=\s*"([^"]*)"');
    for (final match in stringRegex.allMatches(content)) {
      result[match.group(1)!] = match.group(2)!;
    }

    // 解析数字值
    final numberRegex = RegExp(r'(\w+)\s*=\s*(\d+(?:\.\d+)?)');
    for (final match in numberRegex.allMatches(content)) {
      final value = match.group(2)!;
      result[match.group(1)!] =
          value.contains('.') ? double.parse(value) : int.parse(value);
    }

    // 解析布尔值
    final boolRegex = RegExp(r'(\w+)\s*=\s*(true|false)');
    for (final match in boolRegex.allMatches(content)) {
      result[match.group(1)!] = match.group(2)! == 'true';
    }

    // 解析数组
    final arrayRegex = RegExp(r'(\w+)\s*=\s*\{([^}]+)\}');
    for (final match in arrayRegex.allMatches(content)) {
      final arrayName = match.group(1)!;
      final arrayContent = match.group(2)!;

      // 解析字符串数组
      final stringArrayRegex = RegExp(r'"([^"]*)"');
      final items = stringArrayRegex
          .allMatches(arrayContent)
          .map((m) => m.group(1)!)
          .toList();

      if (items.isNotEmpty) {
        result[arrayName] = items;
      }
    }

    return result;
  }

  /// 获取默认完整配置
  Map<String, dynamic> _getDefaultConfig() {
    return {
      'config': {
        'version': '1.0.0',
        'last_updated': '2024-01-15',
        'author': '系统管理员',
      },
      'notice_config': {
        'title': '注意事项',
        'show_refresh_button': true,
        'items': _getDefaultNoticeConfig(),
      },
      'qr_config': {
        'grid_size': 100,
        'detail_size': 220,
        'show_wechat_icon': true,
        'max_count': 9,
      },
      'feature_flags': {
        'enable_qr_recognition': true,
        'enable_hero_animation': true,
        'enable_swipe_navigation': true,
        'enable_long_press_delete': true,
        'enable_tap_to_dismiss': true,
        'enable_hot_reload': true,
      },
    };
  }

  /// 解析Lua配置文件（简单版本，保持向后兼容）
  List<String> _parseLuaConfig(String luaContent) {
    try {
      // 尝试使用高级解析器
      final config = _parseLuaConfigAdvanced(luaContent);
      if (config.containsKey('notice_config')) {
        final noticeConfig = config['notice_config'] as Map<String, dynamic>;
        if (noticeConfig.containsKey('items')) {
          return List<String>.from(noticeConfig['items']);
        }
      }

      // 回退到简单解析
      final regex = RegExp(r'notice_items\s*=\s*\{([^}]+)\}', multiLine: true);
      final match = regex.firstMatch(luaContent);

      if (match != null) {
        final arrayContent = match.group(1)!;
        final itemRegex = RegExp(r'"([^"]+)"');
        final items =
            itemRegex.allMatches(arrayContent).map((m) => m.group(1)!).toList();

        if (items.isNotEmpty) {
          return items;
        }
      }

      return _getDefaultNoticeConfig();
    } catch (e) {
      debugPrint('解析Lua配置失败: $e');
      return _getDefaultNoticeConfig();
    }
  }

  /// 获取完整的Lua配置
  Future<Map<String, dynamic>> getFullConfig({bool forceUpdate = false}) async {
    try {
      // 优先使用本地Lua配置
      if (!forceUpdate) {
        return await loadLocalLuaConfig();
      }

      // 从服务端下载配置
      const url =
          'https://your-server.com/configs/traffic_diversion_config.lua';
      final response = await _dio.get(url);

      if (response.statusCode == 200) {
        final content = response.data.toString();
        return _parseLuaConfigAdvanced(content);
      } else {
        // 回退到本地配置
        return await loadLocalLuaConfig();
      }
    } catch (e) {
      debugPrint('获取完整配置失败: $e');
      // 回退到本地配置
      return await loadLocalLuaConfig();
    }
  }

  /// 获取默认注意事项配置
  List<String> _getDefaultNoticeConfig() {
    return [
      '1.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。',
      '2.请确保上传的微信二维码清晰可见，避免影响学员扫码体验。',
      '3.系统会自动为您的二维码添加微信图标，提升识别度。',
      '4.建议定期更新您的联系方式，确保学员能够及时联系到您。',
      '5.sssss',
    ];
  }

  /// 清除配置缓存
  Future<void> clearCache() async {
    try {
      final cachePath = await _configCachePath;
      final cacheDir = Directory(cachePath);
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
      }
    } catch (e) {
      debugPrint('清除缓存失败: $e');
    }
  }

  /// 模拟Lua配置文件内容（用于测试）
  String get mockLuaConfig => '''
-- 注意事项配置
notice_items = {
  "1.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。",
  "2.请确保上传的微信二维码清晰可见，避免影响学员扫码体验。",
  "3.系统会自动为您的二维码添加微信图标，提升识别度。",
  "4.建议定期更新您的联系方式，确保学员能够及时联系到您。",
  "5.如遇到技术问题，请及时联系客服获取帮助。"
}

-- 其他配置项
config_version = "1.0.0"
last_updated = "2024-01-01"
''';
}
