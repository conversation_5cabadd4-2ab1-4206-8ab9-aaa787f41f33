import 'dart:async';
import 'dart:io';

import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/pages/bluetooth_example_page.dart';
import 'package:npemployee/pages/location_example_page.dart';
import 'package:npemployee/pages/step_count_example_page.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/service/wechat_service.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/mine/change_avatar_sheet.dart';
import 'package:npemployee/widget/mine/wechat_code_dialog.bat';
import 'package:package_info/package_info.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:tencent_calls_uikit/tuicall_kit.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';
import '../../common/dialog/custom_dialog.dart';
import '../../common/widget/calendar/dialog.dart' as calendar;

class PersonalSettingsPage extends StatefulWidget {
  final String avatar;
  const PersonalSettingsPage({super.key, required this.avatar});

  @override
  State<PersonalSettingsPage> createState() => _PersonalSettingsPageState();
}

class _PersonalSettingsPageState extends State<PersonalSettingsPage> {
  File? imageFile;
  String? version;
  StreamSubscription? subscription;

  int _phoneTapCount = 0;
  Timer? _phoneTapTimer;
  String? _joinAt;
  List? _codes;

  void _handlePhoneTap() {
    if (_phoneTapTimer != null && _phoneTapTimer!.isActive) {
      _phoneTapTimer!.cancel();
    }

    _phoneTapCount++;

    if (_phoneTapCount >= 10) {
      _phoneTapCount = 0;
      _triggerSpecialMethod();
    } else {
      _phoneTapTimer = Timer(Duration(seconds: 2), () {
        _phoneTapCount = 0;
      });
    }
  }

  void _handleJoinAtTap() async {
    DateTime? valueTime = DateTime.tryParse(_joinAt ?? '');
    final config = CalendarDatePicker2WithActionButtonsConfig(
        calendarType: CalendarDatePicker2Type.single,
        selectedDayHighlightColor: AppTheme.colorBlue,
        daySplashColor: Colors.transparent,
        hideMonthPickerDividers: true,
        hideYearPickerDividers: true);
    List<DateTime?>? dates = await calendar.showCalendarDatePicker2Dialog(
        context: context,
        join_at_type: _joinAt == null ? 1 : 2,
        config: config,
        value: [valueTime],
        dialogSize: Size(ScreenUtil().screenWidth - 64.w, 300.h));
    if (dates != null && dates.isNotEmpty) {
      String joinTime = dates.first.toString().split(' ').first;
      EasyLoading.show();
      ResultData? res = await UserServiceProvider().setJoinAt(joinTime);
      EasyLoading.dismiss();
      if (res?.code == 0) {
        setState(() {
          _joinAt = joinTime;
        });
      } else {
        EasyLoading.showError(res?.msg ?? '未知错误，请联系管理员');
      }
    }
  }

  /* void _handleWechatCodeTap() {
    showDialog(
        context: context,
        builder: (_) {
          return WechatCodeDialog(
            codes: _codes,
            onSuccess: (value) {
              _codes?.clear();
              setState(() {
                _codes?.add(value);
              });
            },
          );
        });
  } */

  void _triggerSpecialMethod() {
    // 触发的特殊方法
    NavigatorUtils.push(context, CommonRouter.proxySettingPage);
    print('触发了特殊方法');
  }

  @override
  void initState() {
    super.initState();

    _joinAt = GlobalPreferences().userInfo?.user.join_at != null
        ? GlobalPreferences().userInfo!.user.join_at!.split('T').first
        : null;
    _codes = GlobalPreferences().userInfo?.user.wechat_codes ?? [];

    _getPackageInfo();

    subscription = BlocManager().avatarBloc.stream.listen((state) {
      if (state.avatar != null) {
        imageFile = File(state.avatar!);
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    subscription?.cancel();
    subscription = null;
    _phoneTapTimer?.cancel();
    _phoneTapTimer = null;
    super.dispose();
  }

  void _getPackageInfo() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    version = packageInfo.version;
    setState(() {});
  }

  void _showAccountCancellationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CustomDialog(
        title: "提示",
        content: "当前注销账号需联系人工客服协助处理，点击【确定】按钮咨询客服。",
        cancelButtonText: "取消",
        confirmButtonText: "确定",
        cancelButtonColor: AppTheme.colorButtonGrey,
        confirmButtonColor: AppTheme.colorRed,
        onCancel: () {
          Navigator.of(context).pop();
          // Handle additional cancel actions if necessary
          print("Cancellation aborted");
        },
        onConfirm: () {
          Navigator.of(context).pop();
          WeChatService().launchWeChatWork();
        },
      ),
    );
  }

  void _showExitDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CustomDialog(
        title: "提示",
        content: "是否确认退出登录？",
        cancelButtonText: "取消",
        confirmButtonText: "确定",
        cancelButtonColor: AppTheme.colorButtonGrey,
        confirmButtonColor: AppTheme.colorBlue,
        onCancel: () {
          Navigator.of(context).pop();
          // Handle additional cancel actions if necessary
          print("Cancellation aborted");
        },
        onConfirm: () {
          AppInfo().clearLoginStatu();
          Navigator.of(context).pushNamedAndRemoveUntil(
              CommonRouter.loginPage, (route) => false);
        },
      ),
    );
  }

  void _handleAccountCancellation() {
    // Add your account cancellation logic here
    print("Account cancellation logic executed");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: const CommonNav(title: '个人设置'),
      body: Padding(
        padding: const EdgeInsets.symmetric(
            horizontal: 15.0), // Set padding for the whole content
        child: Column(
          children: [
            const SizedBox(
                height: 12), // Add some spacing between app bar and content

            // Profile image cell
            _buildProfileCell(
              title: '头像',
              trailingWidget: imageFile != null
                  ? CircleAvatar(
                      backgroundImage: FileImage(imageFile!),
                      radius: 22.5,
                    )
                  : CircleAvatar(
                      backgroundImage: NetworkImage(
                          GlobalPreferences().userInfo != null
                              ? GlobalPreferences().userInfo?.user.avatar ??
                                  ValidatorUtils.testImageUrl
                              : GlobalPreferences().userLoginModel!.avatar!),
                      radius: 22.5,
                    ),
              showArrow: true,
              onTap: () {
                // Handle tap on profile image
                if (!AppInfo().registered) {
                  ToastUtils.show('请先注册');
                  return;
                }

                if (GlobalPreferences().guestInfo != null) {
                  ToastUtils.show('特邀嘉宾无法更换头像~');
                  return;
                }

                print("Profile image tapped");
                _changeAvatar(context);
              },
            ),
            const SizedBox(height: 12), // Add spacing between cells

            // Name cell
            _buildProfileCell(
              title: '姓名',
              trailingText: GlobalPreferences().userInfo != null
                  ? GlobalPreferences().userInfo!.user.name
                  : GlobalPreferences().userLoginModel!.nickName!,
            ),
            const SizedBox(height: 12), // Add spacing between cells

            // Phone cell
            _buildProfileCell(
                title: '手机号',
                trailingText: GlobalPreferences().userInfo != null
                    ? GlobalPreferences().userInfo!.user.mobile
                    : GlobalPreferences().userLoginModel!.mobile,
                onTap: _handlePhoneTap),
            if (AppInfo().registered) const SizedBox(height: 12),
            if (AppInfo().registered)
              _buildProfileCell(
                  title: '入职时间',
                  showArrow: true,
                  trailingText: _joinAt ?? '请确认您的入职时间',
                  onTap: _handleJoinAtTap),
            /*  if (AppInfo().registered) const SizedBox(height: 12),
            if (AppInfo().registered)
              _buildProfileCell(
                  title: '我的微信码',
                  showArrow: true,
                  trailingText: _codes!.isEmpty ? '点击上传' : null,
                  trailingWidget: _codes!.isEmpty
                      ? null
                      : SizedBox(
                          width: 60,
                          height: 60,
                          child: QrImageView(data: _codes!.first),
                        ),
                  onTap: _handleWechatCodeTap), */
            // Version Update cell
            // _buildProfileCell(
            //   title: '版本更新',
            //   trailingText: version ?? '',
            //   showArrow: true,
            //   onTap: () {
            //     // Handle tap on version update
            //     NavigatorUtils.push(context, CommonRouter.proxySettingPage);
            //     print("Version update tapped");
            //   },
            // ),
            const SizedBox(height: 12),
            _buildProfileCell(
              title: '隐私政策',
              showArrow: true,
              onTap: () {
                // Handle tap on version update
                NavigatorUtils.push(context, CommonRouter.proxyWebview,
                    arguments: {
                      'title': '隐私政策',
                      'url':
                          'https://web.xtjstatic.cn/agreement/privacyAgreement.htm',
                    });
              },
            ),

            const SizedBox(height: 30), // Gap between section and buttons

            // Logout Button
            _buildButton(
              text: '退出登录',
              textColor: AppTheme.colorPrimaryBlack,
              textFont: 15.sp,
              onTap: () {
                _showExitDialog(context);
              },
            ),
            // const SizedBox(height: 16), // Space between buttons
            const Expanded(child: SizedBox()),
            // 弱化注销按钮
            _buildButton(
              text: '账号注销',
              textColor: AppTheme.colorRed,
              bacColor: Colors.transparent,
              textFont: 12.sp,
              onTap: () {
                _showAccountCancellationDialog(context);
              },
            ),
            SizedBox(height: ScreenUtil().bottomBarHeight),
          ],
        ),
      ),
    );
  }

  // Helper function to build a profile cell with title and trailing widget/text
  Widget _buildProfileCell({
    required String title,
    String? trailingText,
    Widget? trailingWidget,
    bool showArrow = false,
    VoidCallback? onTap, // Add a callback for tap events
  }) {
    return GestureDetector(
      onTap: onTap, // Assign the onTap callback to the GestureDetector
      onLongPress: () {
        if (title == '姓名') {
          Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const LocationExamplePage()));
        } else if (title == '手机号') {
          Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const StepCountExamplePage()));
        } else if (title == '隐私政策') {
          Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const BluetoothExamplePage()));
        }
      },
      child: Container(
        height: 60, // Set the fixed height to 60 pixels
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12), // Rounded corners
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style:
                  AppTheme.getTextStyle(baseSize: 15, color: AppTheme.colorGrey)
                      .pfRegular,
            ),
            Row(
              children: [
                if (trailingWidget != null)
                  trailingWidget
                else if (trailingText != null)
                  Text(
                    trailingText,
                    style: AppTheme.getTextStyle(
                            baseSize: 15, color: AppTheme.colorPrimaryBlack)
                        .pfMedium,
                  ),
                if (showArrow) const SizedBox(width: 8),
                if (showArrow)
                  SvgPicture.asset(
                    'assets/svg/mine/profile/right_arrow.svg', // Replace with the correct SVG path
                    width: 7.47, // Adjust size as needed
                    height: 13.58,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Helper function to build the buttons (Logout and Account Cancellation)
  Widget _buildButton({
    required String text,
    required Color textColor,
    required VoidCallback onTap,
    required double textFont,
    Color? bacColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 60, // Set the button height to 60 pixels
        decoration: BoxDecoration(
          color: bacColor ?? Colors.white,
          borderRadius: BorderRadius.circular(12), // Rounded corners
        ),
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(fontSize: textFont, color: textColor).pfRegular,
        ),
      ),
    );
  }

  void _changeAvatar(BuildContext context) async {
    showModalBottomSheet(
        context: context,
        builder: (_) {
          return const ChangeAvatarSheet();
        });
  }
}













/* class PersonalSettingsPage extends StatelessWidget {
  final String avatar;

  const PersonalSettingsPage({super.key, required this.avatar});



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          '个人设置',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(
            horizontal: 15.0), // Set padding for the whole content
        child: Column(
          children: [
            const SizedBox(
                height: 12), // Add some spacing between app bar and content

            // Profile image cell
            _buildProfileCell(
              title: '头像',
              trailingWidget: CircleAvatar(
                backgroundImage: NetworkImage(avatar),
                radius: 22.5,
              ),
              showArrow: true,
              onTap: () {
                // Handle tap on profile image
                print("Profile image tapped");
                _changeAvatar(context);
              },
            ),
            const SizedBox(height: 12), // Add spacing between cells

            // Name cell
            _buildProfileCell(
              title: '姓名',
              trailingText: GlobalPreferences().userInfo != null
                  ? GlobalPreferences().userInfo!['user']['name']
                  : GlobalPreferences().userLoginModel!.nickName!,
            ),
            const SizedBox(height: 12), // Add spacing between cells

            // Phone cell
            _buildProfileCell(
              title: '手机号',
              trailingText: GlobalPreferences().userInfo != null
                  ? GlobalPreferences().userInfo!['user']['mobile']
                  : GlobalPreferences().userLoginModel!.mobile,
            ),
            const SizedBox(height: 12), // Add spacing between cells

            // Version Update cell
            _buildProfileCell(
              title: '版本更新',
              trailingText: 'v1.2.30',
              showArrow: true,
              onTap: () {
                // Handle tap on version update
                print("Version update tapped");
              },
            ),
            const SizedBox(height: 30), // Gap between section and buttons

            // Logout Button
            _buildButton(
              text: '退出登录',
              textColor: AppTheme.colorPrimaryBlack,
              onTap: () {},
            ),
            const SizedBox(height: 16), // Space between buttons

            // Account Cancellation Button
            _buildButton(
              text: '账号注销',
              textColor: AppTheme.colorRed,
              onTap: () {
                _showAccountCancellationDialog(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  // Helper function to build a profile cell with title and trailing widget/text
  Widget _buildProfileCell({
    required String title,
    String? trailingText,
    Widget? trailingWidget,
    bool showArrow = false,
    VoidCallback? onTap, // Add a callback for tap events
  }) {
    return GestureDetector(
      onTap: onTap, // Assign the onTap callback to the GestureDetector
      child: Container(
        height: 60, // Set the fixed height to 60 pixels
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12), // Rounded corners
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: AppTheme.getTextStyle(
                baseSize: 15,
                color: AppTheme.colorGrey,
              ),
            ),
            Row(
              children: [
                if (trailingWidget != null)
                  trailingWidget
                else if (trailingText != null)
                  Text(
                    trailingText,
                    style: AppTheme.getTextStyle(
                      baseSize: 15,
                      color: AppTheme.colorPrimaryBlack,
                    ),
                  ),
                if (showArrow) const SizedBox(width: 8),
                if (showArrow)
                  SvgPicture.asset(
                    'assets/svg/mine/profile/right_arrow.svg', // Replace with the correct SVG path
                    width: 7.47, // Adjust size as needed
                    height: 13.58,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Helper function to build the buttons (Logout and Account Cancellation)
  Widget _buildButton({
    required String text,
    required Color textColor,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 60, // Set the button height to 60 pixels
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12), // Rounded corners
        ),
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(
            fontSize: 14,
            color: textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  void _changeAvatar(BuildContext context) async {
    showModalBottomSheet(
        context: context,
        builder: (_) {
          return ChangeAvatarSheet(onImageDone: (path) {
            NavigatorUtils.pop(context);
          });
        });
  }
} */
