import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/service/lua_config_service.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

/// Lua配置测试页面
/// 用于测试Lua解析功能
class LuaConfigTestPage extends StatefulWidget {
  const LuaConfigTestPage({super.key});

  @override
  State<LuaConfigTestPage> createState() => _LuaConfigTestPageState();
}

class _LuaConfigTestPageState extends State<LuaConfigTestPage> {
  final LuaConfigService _configService = LuaConfigService();
  Map<String, dynamic>? _config;
  List<String> _noticeItems = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadConfig();
  }

  Future<void> _loadConfig() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 加载完整配置
      _config = await _configService.getFullConfig();
      
      // 加载注意事项
      _noticeItems = await _configService.downloadNoticeConfig();
      
      setState(() {});
    } catch (e) {
      EasyLoading.showError('加载配置失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshConfig() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 强制刷新配置
      _config = await _configService.getFullConfig(forceUpdate: true);
      _noticeItems = await _configService.downloadNoticeConfig(forceUpdate: true);
      
      setState(() {});
      EasyLoading.showSuccess('配置刷新成功');
    } catch (e) {
      EasyLoading.showError('刷新配置失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lua配置测试'),
        actions: [
          IconButton(
            onPressed: _isLoading ? null : _refreshConfig,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildConfigSection(),
                  SizedBox(height: 20.h),
                  _buildNoticeSection(),
                  SizedBox(height: 20.h),
                  _buildFeatureFlagsSection(),
                  SizedBox(height: 20.h),
                  _buildQRConfigSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildConfigSection() {
    final config = _config?['config'] as Map<String, dynamic>?;
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '基础配置',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10.h),
            if (config != null) ...[
              _buildConfigItem('版本', config['version']?.toString() ?? 'N/A'),
              _buildConfigItem('更新时间', config['last_updated']?.toString() ?? 'N/A'),
              _buildConfigItem('作者', config['author']?.toString() ?? 'N/A'),
            ] else
              const Text('配置加载失败'),
          ],
        ),
      ),
    );
  }

  Widget _buildNoticeSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '注意事项配置',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10.h),
            Text('共 ${_noticeItems.length} 条注意事项:'),
            SizedBox(height: 10.h),
            ..._noticeItems.asMap().entries.map((entry) {
              return Padding(
                padding: EdgeInsets.only(bottom: 8.h),
                child: Text(
                  entry.value,
                  style: TextStyle(fontSize: 12.sp),
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureFlagsSection() {
    final featureFlags = _config?['feature_flags'] as Map<String, dynamic>?;
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '功能开关',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10.h),
            if (featureFlags != null) ...[
              ...featureFlags.entries.map((entry) {
                return _buildConfigItem(
                  entry.key,
                  entry.value.toString(),
                  isBoolean: entry.value is bool,
                  boolValue: entry.value as bool?,
                );
              }).toList(),
            ] else
              const Text('功能开关配置加载失败'),
          ],
        ),
      ),
    );
  }

  Widget _buildQRConfigSection() {
    final qrConfig = _config?['qr_config'] as Map<String, dynamic>?;
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '二维码配置',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10.h),
            if (qrConfig != null) ...[
              ...qrConfig.entries.map((entry) {
                return _buildConfigItem(entry.key, entry.value.toString());
              }).toList(),
            ] else
              const Text('二维码配置加载失败'),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigItem(
    String key, 
    String value, {
    bool isBoolean = false,
    bool? boolValue,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120.w,
            child: Text(
              '$key:',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: isBoolean
                ? Row(
                    children: [
                      Icon(
                        boolValue == true ? Icons.check_circle : Icons.cancel,
                        color: boolValue == true ? Colors.green : Colors.red,
                        size: 16.sp,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        value,
                        style: TextStyle(fontSize: 12.sp),
                      ),
                    ],
                  )
                : Text(
                    value,
                    style: TextStyle(fontSize: 12.sp),
                  ),
          ),
        ],
      ),
    );
  }
}
